import { gql } from '../../utils'
import { HOME_PRODUCT_FRAGMENT, PRODUCT_PRICE_RANGE_FRAGMENT } from '../fragments'

/**
 * 获取首页配置信息
 * model_type 模块类型：1:图片和产品,2:图片轮播,3:图片瀑布流,4:固定图标,5:领券
 */
export const GET_HOME_V2 = gql`
  query getHomeV2 {
    homeV2 {
      HomePageCompontent {
        id
        title
        identifier
        components {
          id
          title
          model_type
          button_text
          is_display
          display_mode
          banner_display_mode
          url {
            type
            url
            value
            miniprogram {
              appid
              name
              pagepath
              title
              version
            }
          }
          start_at
          end_at
          activity_end_at
          activity_start_at
          relation_items {
            name
            button_arrow
            button_text
            button_url {
              type
              url
              value
              miniprogram {
                appid
                name
                pagepath
                title
                version
              }
            }
            description
            id
            image_url
            subtitle
            title
            video_url
            fullscreen
            image_ratio_mobile {
              height
              width
            }
            image_url_desktop
            image_ratio_desktop {
              height
              width
            }
          }
          products {
            product {
              ...homeProductFragment
              ...productPriceRangeFragment
              custom_attributesV3(
                filters: {
                  filter_attributes: [
                    "product_tag"
                    "paymeng_method"
                    "max_usage_limit_ncoins"
                    "is_insurance"
                    "insurance_link"
                  ]
                }
              ) {
                items {
                  code
                  ... on AttributeValue {
                    value
                  }
                  ... on AttributeSelectedOptions {
                    selected_options {
                      label
                      value
                    }
                  }
                }
              }
              ... on ConfigurableProduct {
                variants {
                  product {
                    paymeng_method
                    special_from_date_timestamp
                    special_to_date_timestamp
                    new_from_date
                    new_to_date
                    new_from_date_timestamp
                    new_to_date_timestamp
                    stock_status
                    ...productPriceRangeFragment
                  }
                }
              }
            }
            url {
              type
              url
              value
              miniprogram {
                appid
                name
                pagepath
                title
                version
              }
            }
          }
          coupon_rules {
            rule_id
            status
            title
            description
            discount
            discount_type
            status_label
            rule_label
            is_ncoin
          }
          rankings {
            id
            title
            url {
              url
              type
              value
              miniprogram {
                title
                name
                appid
                pagepath
                version
              }
            }
            position
            products {
              top
              url {
                url
                type
                value
                miniprogram {
                  title
                  name
                  appid
                  pagepath
                  version
                }
              }
              position
              product {
                ...homeProductFragment
                ...productPriceRangeFragment
                ... on ConfigurableProduct {
                  variants {
                    product {
                      paymeng_method
                      special_from_date_timestamp
                      special_to_date_timestamp
                      new_from_date
                      new_to_date
                      new_from_date_timestamp
                      new_to_date_timestamp
                      stock_status
                      ...productPriceRangeFragment
                    }
                  }
                }
              }
            }
          }
        }
      }
      homeNavigation {
        page_identifier
        relation_category_uid
        title
        show_products
        top_categories {
          redirect {
            type
            value
            url
          }
          title
        }
      }
    }
  }
  ${HOME_PRODUCT_FRAGMENT}
  ${PRODUCT_PRICE_RANGE_FRAGMENT}
`
