'use client'

import { useEffect, useMemo, useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import { IconUsed, OrderPickupInfo, OrderVouchers } from '@ninebot/core'
import { Carousel } from 'antd'
import { CarouselRef } from 'antd/es/carousel'

import { CustomImage, IconArrow, Modal } from '@/components'

/**
 * 券码弹窗组件的属性类型定义
 */
type CouponPopupProps = {
  /** 控制弹窗显示/隐藏状态 */
  popupVisible: boolean
  /** 复制文本到剪贴板的回调函数 */
  copyToClipboard: (text: string) => void
  /** 关闭弹窗的回调函数 */
  closePopup: () => void
  /** 自提取货码信息（当存在时为取货码模式） */
  pickupInfo?: OrderPickupInfo
  /** 券码列表（当存在时为券码模式） */
  couponList?: OrderVouchers
  /** 商品信息，用于显示商品图片和名称 */
  productInfo?: {
    name: string
    image: string
  }
}

/**
 * 券码弹窗组件
 * 支持两种模式：
 * 1. 取货码模式：显示单个取货码
 * 2. 券码模式：显示多个券码，支持轮播切换
 */
const CouponPopup = (props: CouponPopupProps) => {
  // 解构组件属性
  const {
    popupVisible,
    copyToClipboard,
    closePopup,
    pickupInfo,
    couponList = [],
    productInfo,
  } = props

  // 国际化翻译函数
  const getI18nString = useTranslations('Common')

  // 轮播组件引用
  const swiperRef = useRef<CarouselRef>(null)

  // 当前显示的券码索引
  const [currentIndex, setCurrentIndex] = useState(0)

  // 轮播动画状态，防止快速切换
  const [isAnimating, setIsAnimating] = useState(false)

  /**
   * 判断当前是否为取货码模式
   * @returns {boolean} 如果存在pickupInfo则为取货码模式，否则为券码模式
   */
  const isPickupCode = useMemo(() => {
    return !!pickupInfo
  }, [pickupInfo])

  /**
   * 获取当前显示的验证码
   * @returns {string | null | undefined} 当前显示的验证码
   */
  const currentCode = useMemo(() => {
    return isPickupCode ? pickupInfo?.code : couponList?.[currentIndex]?.code
  }, [isPickupCode, pickupInfo?.code, couponList, currentIndex])

  /**
   * 生成轮播图数据
   * 根据模式不同生成不同的数据结构
   * @returns {Array} 轮播图数据数组
   */
  const carouselData = useMemo(() => {
    // 取货码模式：只有一个取货码
    if (pickupInfo) {
      return [
        {
          image_url: pickupInfo?.qr_code,
          imageStyle: pickupInfo?.status === '0' ? {} : { opacity: 0.08 },
          children:
            pickupInfo?.status !== '0' ? (
              <div className="absolute bottom-[8px] right-[8px]">
                <IconUsed />
                <span className="absolute bottom-[24px] right-[8px] -rotate-[40deg] text-[20px] text-[#86868B]">
                  {pickupInfo?.status_label}
                </span>
              </div>
            ) : null,
        },
      ]
    }
    // 券码模式：多个券码
    else {
      return couponList?.map((coupon) => {
        return {
          image_url: coupon?.qr_code,
          imageStyle: coupon?.status === '0' ? {} : { opacity: 0.08 },
          children:
            coupon?.status !== '0' ? (
              <div className="absolute bottom-[8px] right-[8px]">
                <IconUsed />
                <span className="absolute bottom-[24px] right-[8px] -rotate-[40deg] text-[20px] text-[#86868B]">
                  {coupon?.status_label}
                </span>
              </div>
            ) : null,
        }
      })
    }
  }, [pickupInfo, couponList])

  /**
   * 判断当前验证码是否未使用
   * @returns {boolean} true表示未使用，false表示已使用
   */
  const isUnUsed = useMemo(() => {
    if (isPickupCode) {
      return pickupInfo?.status === '0'
    }
    return couponList?.[currentIndex]?.status === '0'
  }, [isPickupCode, pickupInfo?.status, couponList, currentIndex])

  /**
   * 处理轮播图切换动画状态
   * 设置动画状态，防止用户快速连续点击导致的异常
   */
  const handleSwipeAnimation = () => {
    setIsAnimating(true)
    // 300ms后重置动画状态，与轮播动画时长保持一致
    setTimeout(() => setIsAnimating(false), 300)
  }

  /**
   * 切换到上一张券码
   * 包含边界检查和错误处理
   */
  const handlePrevPress = () => {
    // 检查是否可以向前切换且不在动画中
    if (currentIndex > 0 && !isAnimating) {
      try {
        const newIndex = Math.max(currentIndex - 1, 0)
        setCurrentIndex(newIndex)
        swiperRef.current?.goTo(newIndex, false)
        handleSwipeAnimation()
      } catch (error) {
        console.error('切换上一张失败:', error)
      }
    }
  }

  /**
   * 切换到下一张券码
   * 包含边界检查和错误处理
   */
  const handleNextPress = () => {
    const maxIndex = (couponList?.length || 0) - 1
    // 检查是否可以向后切换且不在动画中
    if (currentIndex < maxIndex && !isAnimating) {
      try {
        const newIndex = Math.min(currentIndex + 1, maxIndex)
        setCurrentIndex(newIndex)
        swiperRef.current?.goTo(newIndex, false)
        handleSwipeAnimation()
      } catch (error) {
        console.error('切换下一张失败:', error)
      }
    }
  }

  /**
   * 监听弹窗显示状态变化
   * 当弹窗关闭时重置轮播到第一张
   */
  useEffect(() => {
    if (!popupVisible) {
      swiperRef.current?.goTo(0)
      setCurrentIndex(0)
    }
  }, [popupVisible])

  return (
    <Modal
      isOpen={popupVisible}
      width={600}
      onClose={closePopup}
      okButtonProps={{ style: { display: 'none' } }}
      cancelButtonProps={{ style: { display: 'none' } }}
      title={getI18nString('coupon_code')}>
      <>
        {/* 分割线 */}
        <div className="-mt-base-16 mb-base-16 h-[1px] w-full bg-[#F3F3F4]"></div>

        <div className="mx-auto w-[420px]">
          {/* 商品信息展示区域 */}
          {productInfo && (
            <div className="mb-[16px] flex items-center justify-between">
              {/* 商品图片 */}
              <CustomImage
                width={60}
                height={60}
                className="mr-[16px] rounded-[8px]"
                src={productInfo.image}
                alt={productInfo.name}
              />

              {/* 商品描述 */}
              <div className="flex-1 font-miSansDemiBold450 text-[16px] leading-[22px] text-[#0F0F0F]">
                上门/取车维修免费{isPickupCode ? '1' : couponList?.length}次
              </div>

              {/* 验证码总数显示 */}
              <div className="text-[16px] leading-[1.4] text-[#000000]">
                {getI18nString('verification_code_total')}
                <span className="font-miSansDemiBold450 text-[20px] text-primary">
                  {isPickupCode ? '1' : couponList?.length}
                </span>
                {getI18nString('piece')}
              </div>
            </div>
          )}

          {/* 券码卡片主体 */}
          <div className="relative flex flex-col items-center rounded-base-12 bg-[#f8f8f9]">
            {/* 券码信息区域 */}
            <div className="relative w-full py-[16px]">
              {/* 左右两侧的圆形装饰 */}
              <div className="absolute -bottom-[8px] -left-[8px] h-[16px] w-[16px] rounded-full bg-white" />
              <div className="absolute -bottom-[8px] -right-[8px] h-[16px] w-[16px] rounded-full bg-white" />

              <div className="flex items-center">
                {/* 券码序号显示 */}
                <div className="min-w-[76px] px-[12px] py-[18px] text-center text-[14px] leading-[20px] text-[#0F0F0F]">
                  {getI18nString('verification_code_index', { key: currentIndex + 1 })}
                </div>

                {/* 虚线分割 */}
                <div className="h-[56px] w-[1px] border-l border-dashed border-[#E1E1E4] bg-transparent" />

                {/* 券码详情和复制按钮 */}
                <div className="flex flex-1 items-center justify-between gap-base px-8">
                  <div className="flex flex-col gap-base">
                    {/* 券码文本 */}
                    <div className="font-miSansDemiBold450 text-[20px] leading-[1.4] text-[#0F0F0F]">
                      {currentCode}
                    </div>

                    {/* 过期时间（仅券码模式显示） */}
                    <div className="font-miSansRegular330 text-[12px] leading-[16px] text-[#6E6E73]">
                      {`${getI18nString('expired_time_until')}${
                        isPickupCode
                          ? pickupInfo?.start_date
                            ? pickupInfo?.start_date + '至' + pickupInfo?.expired_date
                            : pickupInfo?.expired_date
                          : couponList?.[currentIndex]?.expired_at?.split(' ')[0]
                      }`}
                    </div>
                  </div>

                  {/* 复制按钮（仅未使用状态显示） */}
                  {isUnUsed && (
                    <button
                      className="flex h-[32px] items-center justify-center whitespace-nowrap rounded-full border border-primary px-base-16"
                      onClick={() => copyToClipboard(currentCode || '')}>
                      <div className="font-miSansRegular330 text-[14px] leading-[20px] text-primary">
                        {getI18nString('copy')}
                      </div>
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* 虚线分割 */}
            <div className="h-[1px] w-[381px] border-b border-dashed border-[#E1E1E4] bg-transparent" />

            {/* 二维码轮播区域 */}
            <div className="mb-[32px] mt-[24px] w-full flex-1">
              <div className="mx-auto h-[180px] w-[180px]">
                <Carousel
                  ref={swiperRef}
                  dots={false}
                  beforeChange={(from, to) => {
                    console.log('轮播即将从', from, '切换到', to)
                    setCurrentIndex(to)
                  }}
                  afterChange={(current) => {
                    console.log('轮播已切换到', current)
                    setCurrentIndex(current)
                  }}>
                  {carouselData?.map((coupon, index) => {
                    // 获取当前券码的使用状态
                    const currentCouponStatus = isPickupCode
                      ? pickupInfo?.status
                      : couponList?.[index]?.status
                    const isCurrentUnused = currentCouponStatus === '0'

                    return (
                      <div key={index} className="relative">
                        {/* 二维码图片 */}
                        <CustomImage
                          displayMode="responsive"
                          src={coupon.image_url || ''}
                          className={`h-full w-full ${!isCurrentUnused ? 'opacity-[0.08]' : ''}`}
                          style={{ ...coupon.imageStyle }}
                          alt="券码二维码"
                        />
                        {/* 已使用标识 */}
                        {coupon.children}
                      </div>
                    )
                  })}
                </Carousel>
              </div>
            </div>

            {/* 左侧切换按钮（多张券码时显示） */}
            {Number(carouselData?.length) > 1 && (
              <button
                className={`absolute -left-[26px] top-1/2 flex h-16 w-16 -translate-x-full -translate-y-1/2 items-center justify-center rounded-full transition-all duration-200 ${
                  currentIndex === 0
                    ? 'cursor-not-allowed bg-[#00000008]'
                    : 'bg-[#00000014] hover:bg-[#00000020]'
                }`}
                onClick={currentIndex === 0 ? undefined : handlePrevPress}
                disabled={currentIndex === 0}
                aria-label="上一张券码">
                <IconArrow
                  size={20}
                  rotate={90}
                  color={currentIndex === 0 ? '#BBBBBD' : '#000000'}
                />
              </button>
            )}

            {/* 右侧切换按钮（多张券码时显示） */}
            {Number(carouselData?.length) > 1 && (
              <button
                className={`absolute -right-[26px] top-1/2 flex h-16 w-16 -translate-y-1/2 translate-x-full items-center justify-center rounded-full transition-all duration-200 ${
                  currentIndex === (couponList?.length || 0) - 1
                    ? 'cursor-not-allowed bg-[#00000008]'
                    : 'bg-[#00000014] hover:bg-[#00000020]'
                }`}
                onClick={
                  currentIndex === (couponList?.length || 0) - 1 ? undefined : handleNextPress
                }
                disabled={currentIndex === (couponList?.length || 0) - 1}
                aria-label="下一张券码">
                <IconArrow
                  size={20}
                  rotate={-90}
                  color={currentIndex === (couponList?.length || 0) - 1 ? '#BBBBBD' : '#000000'}
                />
              </button>
            )}

            {/* 底部信息显示 */}
            <div className="absolute -bottom-base-16 left-0 right-0 translate-y-full text-center text-[16px] leading-[1.4] text-[#000000]">
              {couponList && couponList.length > 1
                ? `${currentIndex + 1}/${couponList?.length}`
                : null}
            </div>
          </div>
        </div>
      </>
    </Modal>
  )
}

export default CouponPopup
