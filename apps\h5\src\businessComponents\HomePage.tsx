'use client'

import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import {
  appSessionStorage,
  cn,
  resolveCatchMessage,
  TCatchMessage,
  TRACK_EVENT,
  useAuth,
  useCurrentTime,
  useLazyGetHomeV2Query,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'
import {
  CurrentComponents,
  HomeFormatedNavigation,
  HomeFormatedNavigations,
  HomePageComponents,
  TabItem,
} from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'

import { Footer, Header, HomeEmpty, HomeModule, HomeSkeleton, ScrollNavTabs } from '@/components'
import { formatComponents, formatHomeNavigation } from '@/utils/format'

export default function HomePage() {
  const getCarRef = useRef(false)
  const { timestamp: currentTime, fetchCurrentTime } = useCurrentTime()
  const [getHomeV2] = useLazyGetHomeV2Query()
  const { reportEvent } = useVolcAnalytics()

  const { isLoggedIn, isAuthLoading } = useAuth()
  const toast = useToastContext()
  const { openPage } = useNavigate()

  const [isScrolled, setIsScrolled] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isFetching, setIsFetching] = useState(false)
  const [hasCar, setHasCar] = useState(false)
  const [currentIdentifier, setCurrentIdentifier] = useState<string | null | undefined>(null)
  const [homeNavigation, setHomeNavigation] = useState<HomeFormatedNavigations>([])
  const [homeNavs, setHomeNavs] = useState<TabItem[]>([])
  const [homeComponents, setHomeComponents] = useState<{
    [key: string]: HomePageComponents
  }>({})
  const [categoryAllUid, setCategoryAllUid] = useState<string | null | undefined>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isLoadingHome, setIsLoadingHome] = useState(true)
  const [isFirstCarousel, setIsFirstCarousel] = useState(false)

  /**
   * 从appSessionStorage恢复tab状态
   */
  const restoreTabState = useCallback(async () => {
    try {
      const savedIdentifier = await appSessionStorage.getItem('homeTabIdentifier')
      const savedIndex = await appSessionStorage.getItem('homeTabIndex')

      return {
        identifier: savedIdentifier as string | null,
        index: savedIndex as number | null,
      }
    } catch (error) {
      console.error('Failed to restore tab state from appSessionStorage:', error)
      return { identifier: null, index: null }
    }
  }, [])

  /**
   * 保存tab状态到appSessionStorage
   */
  const saveTabState = useCallback(async (identifier: string | null | undefined, index: number) => {
    try {
      await appSessionStorage.setItem('homeTabIdentifier', identifier || '')
      await appSessionStorage.setItem('homeTabIndex', index)
    } catch (error) {
      console.error('Failed to save tab state to appSessionStorage:', error)
    }
  }, [])

  /**
   * 设置状态
   */
  const handleSetState = useCallback(
    async (item: HomeFormatedNavigation, index: number) => {
      setCurrentIndex(index)
      setCurrentIdentifier(item.value)
      await saveTabState(item.value, index)
      setHomeNavs(
        item?.topCategories?.map((item, index) => ({
          title: item.label,
          sort: index,
          redirect: {
            type: item.type,
            value: item.value,
            url: item.url,
          },
        })),
      )
      setCategoryAllUid(item.categoryAllUid)
    },
    [saveTabState],
  )

  /**
   * 关闭laoding
   */
  const handleCloseLoading = useCallback(() => {
    setIsLoading(false)
    setIsFetching(false)
    setIsLoadingHome(false)
  }, [])

  /**
   * 获取首页模块配置
   */
  const getHomePageConfigs = useCallback(
    async (identifier: string, hadCar: boolean) => {
      if (!identifier) {
        handleCloseLoading()
        return
      }

      try {
        // 检查是否已经有该页面的组件数据
        if (homeComponents[identifier]?.length) {
          // 如果已有数据，直接使用
          const components = homeComponents[identifier]
          setIsFirstCarousel(components[0]?.model_type === 2)
          handleCloseLoading()
          return
        }

        // 如果没有数据，重新获取完整的首页数据
        const newCurrentTime = await fetchCurrentTime()
        const results = await Promise.all([getHomeV2({}).unwrap()])
        const [homeData] = results

        if (homeData?.homeV2) {
          const {
            HomePageCompontent: homePageCompontents = [],
            homeNavigation: homeNavigations = [],
          } = homeData.homeV2

          const newHomeTopNavigation = formatHomeNavigation(hadCar, homeNavigations || [])
          setHomeNavigation(newHomeTopNavigation)

          const newComponentsMap = (homePageCompontents || []).filter(Boolean).reduce(
            (acc, item) => {
              if (item?.identifier && item?.components) {
                acc[item.identifier] = formatComponents(item.components, newCurrentTime)
              }
              return acc
            },
            {} as { [key: string]: HomePageComponents },
          )

          setHomeComponents((prev) => ({
            ...prev,
            ...newComponentsMap,
          }))

          // 设置当前页面的轮播状态
          const currentComponents = newComponentsMap[identifier]
          if (currentComponents?.length) {
            setIsFirstCarousel(currentComponents[0]?.model_type === 2)
          }

          const currentNav = newHomeTopNavigation?.find((item) => item.value === identifier)
          if (currentNav && newHomeTopNavigation) {
            const navIndex = newHomeTopNavigation.indexOf(currentNav)
            setCurrentIndex(navIndex)
            setCurrentIdentifier(currentNav.value)
            const formattedNavs = (currentNav.topCategories || []).map((item, index) => ({
              title: item.label,
              sort: index + 1,
              redirect: {
                type: item.type,
                value: item.value,
                url: item.url,
              },
            }))
            setHomeNavs(formattedNavs)
            setCategoryAllUid(currentNav.categoryAllUid || '')
            await saveTabState(currentNav.value, navIndex)
          }
        }

        handleCloseLoading()
      } catch (error) {
        handleCloseLoading()
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })
      }
    },
    [toast, fetchCurrentTime, getHomeV2, handleCloseLoading, saveTabState, homeComponents],
  )

  /**
   * 获取用户是否有车
   */
  const getHasCar = useCallback(
    async (isRefresh = false) => {
      try {
        setIsLoadingHome(true)
        const newCurrentTime = await fetchCurrentTime()
        const results = await Promise.all([getHomeV2({}).unwrap()])
        const [homeData] = results

        if (homeData?.homeV2) {
          const {
            HomePageCompontent: homePageCompontents = [],
            homeNavigation: homeNavigations = [],
          } = homeData.homeV2

          // 根据登录状态和导航数据判断用户是否有车
          // 如果有多个导航且用户已登录，假设用户有车
          const hadCar = isLoggedIn && (homeNavigations?.length || 0) > 1

          const shouldUpdate = !isRefresh || hadCar !== hasCar
          if (shouldUpdate) {
            const newHomeTopNavigation = formatHomeNavigation(hadCar, homeNavigations || [])
            setHomeNavigation(newHomeTopNavigation)

            const newComponentsMap = (homePageCompontents || [])
              .filter(Boolean) // 排除 null/undefined
              .reduce(
                (acc, item) => {
                  if (item?.identifier && item?.components) {
                    acc[item.identifier] = formatComponents(item.components, newCurrentTime)
                  }
                  return acc
                },
                {} as { [key: string]: HomePageComponents },
              )

            setHomeComponents((prev) => ({
              ...prev,
              ...newComponentsMap,
            }))

            // 恢复保存的tab状态
            const savedState = await restoreTabState()
            const targetIdentifier = savedState.identifier || newHomeTopNavigation?.[0]?.value
            const targetIndex = savedState.index !== null ? savedState.index : 0

            const targetNav = newHomeTopNavigation?.find((nav) => nav.value === targetIdentifier)
            if (targetNav) {
              setCurrentIdentifier(targetIdentifier)
              setCurrentIndex(targetIndex)
              // 转换topCategories为TabItem格式
              const formattedNavs = (targetNav.topCategories || []).map((item, index) => ({
                title: item.label,
                sort: index + 1,
                redirect: {
                  type: item.type,
                  value: item.value,
                  url: item.url,
                },
              }))
              setHomeNavs(formattedNavs)
              setCategoryAllUid(targetNav.categoryAllUid || '')

              const isCarousel = newComponentsMap[targetIdentifier as string]?.[0]?.model_type === 2
              setIsFirstCarousel(isCarousel)
            }
          }

          setHasCar(hadCar)
        }

        handleCloseLoading()
      } catch (error) {
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })
        handleCloseLoading()
      }
    },
    [getHomeV2, fetchCurrentTime, hasCar, toast, isLoggedIn, restoreTabState, handleCloseLoading],
  )

  /**
   * 切换tab
   */
  const handleToggleTab = useCallback(
    async (item: HomeFormatedNavigation, index: number) => {
      await handleSetState(item, index)
      if (homeComponents.hasOwnProperty(item.value as string)) {
        const components = homeComponents[item.value as string] || []
        setIsFirstCarousel(components[0]?.model_type === 2)
      } else {
        setIsLoadingHome(true)
        setIsFirstCarousel(false)
        await getHomePageConfigs(item.value as string, hasCar)
      }

      // 滚动到页面顶部
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      })
    },
    [hasCar, homeComponents, handleSetState, getHomePageConfigs],
  )

  /**
   * 获取首页配置
   */
  useEffect(() => {
    if (!getCarRef.current && !isAuthLoading) {
      getHasCar()
      getCarRef.current = true
    }
  }, [getHasCar, isAuthLoading])

  /**
   * 组件卸载时保存当前状态
   */
  useEffect(() => {
    return () => {
      if (currentIdentifier && currentIndex >= 0) {
        saveTabState(currentIdentifier, currentIndex)
      }
    }
  }, [currentIdentifier, currentIndex, saveTabState])

  /**
   * 页面初始化时滚动到顶部
   */
  useEffect(() => {
    // 页面加载完成后滚动到顶部
    window.scrollTo({
      top: 0,
      behavior: 'auto', // 使用auto避免初始加载时的动画
    })
  }, [])

  // 监听滚动
  useEffect(() => {
    const handleScroll = () => {
      // 当滚动超过100px时切换状态
      setIsScrolled(window.scrollY > 100)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  /**
   * 格式化模块数据
   */
  const currentHomeComponents = useMemo(() => {
    if (currentIdentifier) {
      const currentComponents = homeComponents[currentIdentifier]
      if (currentComponents?.length) {
        return currentComponents
      }

      return []
    }

    return []
  }, [homeComponents, currentIdentifier])

  /**
   * 埋点：打开商城
   */
  useEffect(() => {
    if (currentIdentifier) {
      reportEvent(TRACK_EVENT.shop_home_page_exposure, {
        store_name: currentIdentifier,
      })
    }
  }, [currentIdentifier, reportEvent])

  return (
    <div className="relative">
      {isLoading ? (
        <HomeSkeleton />
      ) : (
        <>
          <div
            className={cn(
              'max-container',
              isFirstCarousel
                ? 'fixed top-0 z-[1001] transition-all duration-300'
                : 'sticky top-0 z-50 h-[108px]',
              !isFirstCarousel || isScrolled ? 'bg-white/95 backdrop-blur-sm' : 'bg-transparent',
            )}>
            <Header
              isTransparent={isFirstCarousel && !isScrolled}
              headerTabs={homeNavigation}
              currentIndex={currentIndex}
              currentIdentifier={currentIdentifier as string}
              handleToggleTab={handleToggleTab}
              isLoading={isLoading}
            />

            <ScrollNavTabs
              tabs={homeNavs}
              showMore={true}
              isTransparent={isFirstCarousel && !isScrolled}
              onTabChange={(item) => {
                if (typeof item === 'string') {
                  return
                }

                openPage({
                  type: item.redirect.type as string,
                  url: item.redirect.url || '',
                  value: item.redirect.value || '',
                })
              }}
              categoryAllUid={categoryAllUid as string}
              position="left"
              className="px-[16px] pb-8 pt-base-12"
              isHome={true}
            />
          </div>

          {currentHomeComponents.length ? (
            <HomeModule
              isRefresh={isFetching}
              currentTime={currentTime}
              components={currentHomeComponents as CurrentComponents[]}
              isHome={true}
            />
          ) : isLoadingHome ? (
            <HomeSkeleton showHeader={false} />
          ) : (
            <HomeEmpty />
          )}

          <Footer />
        </>
      )}
    </div>
  )
}
