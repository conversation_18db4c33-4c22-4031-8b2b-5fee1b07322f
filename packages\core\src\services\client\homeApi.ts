import {
  GET_FOOTER_NEWS,
  GET_HOME_PAGE_CONFIG,
  GET_HOME_TOP_NAVIGATION,
  GET_HOME_V2,
  GET_SITE_CONFIG,
  GET_THEMATIC_PAGE_CONFIG,
} from '../../graphql'
import {
  GetFooterNewsQuery,
  GetFooterNewsQueryVariables,
  GetHomePageConfigQuery,
  GetHomePageConfigQueryVariables,
  GetHomeTopNavigationQuery,
  GetHomeTopNavigationQueryVariables,
  GetHomeV2Query,
  GetHomeV2QueryVariables,
  GetSiteConfigQuery,
  GetSiteConfigQueryVariables,
  GetThematicPageConfigQuery,
  GetThematicPageConfigQueryVariables,
} from '../../graphql/generated/graphql'
import rootApi from '../../utils/rootApi'

/**
 * homepage/专区页 查询/操作相关 api
 */
const homePageApi = rootApi.injectEndpoints({
  endpoints: (build) => ({
    /**
     * 获取首页配置
     */
    getHomePageConfig: build.query<GetHomePageConfigQuery, GetHomePageConfigQueryVariables>({
      query: (variables) => ({
        document: GET_HOME_PAGE_CONFIG,
        variables,
      }),
      extraOptions: { isAuth: false },
    }),
    /**
     * 获取首页顶部导航配置
     */
    getHomeTopNavigation: build.query<
      GetHomeTopNavigationQuery,
      GetHomeTopNavigationQueryVariables
    >({
      query: () => ({
        document: GET_HOME_TOP_NAVIGATION,
      }),
      extraOptions: { isAuth: false },
    }),
    /**
     * 获取专区页配置
     */ getThematicPageConfig: build.query<
      GetThematicPageConfigQuery,
      GetThematicPageConfigQueryVariables
    >({
      query: (variables) => ({
        document: GET_THEMATIC_PAGE_CONFIG,
        variables,
      }),
      extraOptions: { isAuth: false },
    }),
    /**
     * 获取footer 配置
     */
    getFooterNews: build.query<GetFooterNewsQuery, GetFooterNewsQueryVariables>({
      query: () => ({
        document: GET_FOOTER_NEWS,
      }),
      extraOptions: { isAuth: false },
    }),
    /**
     * 获取site config
     */
    getSiteConfig: build.query<GetSiteConfigQuery, GetSiteConfigQueryVariables>({
      query: () => ({
        document: GET_SITE_CONFIG,
      }),
      extraOptions: { isAuth: false },
    }),
    /**
     * 获取首页v2
     */
    getHomeV2: build.query<GetHomeV2Query, GetHomeV2QueryVariables>({
      query: (variables) => ({
        document: GET_HOME_V2,
        variables,
      }),
      extraOptions: { isAuth: false },
    }),
  }),
})

export const {
  useGetHomePageConfigQuery,
  useLazyGetHomePageConfigQuery,
  useLazyGetHomeTopNavigationQuery,
  useGetThematicPageConfigQuery,
  useGetFooterNewsQuery,
  useGetSiteConfigQuery,
} = homePageApi

export default homePageApi
