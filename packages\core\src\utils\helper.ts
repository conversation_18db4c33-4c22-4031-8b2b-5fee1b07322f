/* ------------------------------- 与业务相关的辅助函数 ------------------------------- */
import Big from 'big.js'
import JSEncrypt from 'jsencrypt'
import cloneDeep from 'lodash-es/cloneDeep'

import { getAppConfig } from '../config'
import { TCartProductItem } from '../store'
import {
  TCheckoutCartProductItem,
  TCheckoutCartProductItems,
} from '../store/modules/checkout/checkoutSlice'
import {
  type CurrentComponents,
  type HomePageComponents,
  type HomeProduct,
  type HomeTopNavigation,
} from '../typings'

import BrowserCookie from './cookie'
import {
  getAppVersion,
  getOSSUrl,
  getPublicKey,
  getShopApiUrl,
  getShopApiUrlBasicAuth,
} from './envUtil'
import { GqlRequest } from './graphql'
import BrowserStorage from './storage'
import { encodeBase64, generateRandomString, isServer } from './util'

/**
 * 获取 encode 后的 商城 Api URL 认证信息
 */
export const getEncodeShopUrlBasicAuth = () => {
  return `Basic ${encodeBase64(getShopApiUrlBasicAuth()!)}`
}

/**
 * 拼接生成 OSS URL
 */
export const generateOSSUrl = (url: string) => {
  const ossUrl = getOSSUrl()
  let safeUrl = url.startsWith('/') ? url : `/${url}`

  if (safeUrl.includes('?')) {
    safeUrl = safeUrl + '&version=' + getAppVersion()
  }

  return `${ossUrl ? ossUrl : getShopApiUrl() + '/'}${safeUrl}?version=${getAppVersion()}`
}

/**
 * 生成 QUID
 */
export const generateQUID = (platform: string = 'web') => {
  const timestamp = Date.now()
  const randomString = generateRandomString(6)
  return platform + '_' + timestamp + '_' + randomString
}

/**
 * 生成加密的 QUID
 */
export const generateEncryptedQUID = (platform: string = 'web') => {
  const encrypt = new JSEncrypt()
  const QUID = generateQUID(platform)
  encrypt.setPublicKey(getPublicKey() ?? '')
  return encrypt.encrypt(QUID)
}

/**
 * 商城 graphql 请求实例
 */
export const gqlRequest = new GqlRequest(`${getShopApiUrl()}/graphql`, {})

/**
 * 生成 local storage 实例
 */
export const appLocalStorage = new BrowserStorage(
  getAppConfig().appName,
  isServer ? null : localStorage,
)

/**
 * 生成 session storage 实例
 */
export const appSessionStorage = new BrowserStorage(
  getAppConfig().appName,
  isServer ? null : sessionStorage,
)

/**
 * 生成 cookie 实例
 */
export const appCookie = new BrowserCookie('')

/**
 * 验证产品是上/下架状态
 */
export const validateProductStatus = (product: TCartProductItem['product'], status = 1) => {
  return product?.status === status
}

/**
 * 验证产品是否有库存
 */
export const validateProductStock = (product: TCartProductItem['product'], status = 'IN_STOCK') => {
  return product?.stock_status === status
}

/**
 * 验证产品是否有销售库存
 */
export const validateProductSalableQty = (product: TCartProductItem['product']) => {
  return (product?.salable_qty ?? 0) > 0
}

/**
 * 格式化产品数据
 */
export const formatProduct = (product: TCheckoutCartProductItem) => {
  const cloneProduct = cloneDeep({
    ...product,
    // NOTE: 可将 extension_info 读取为其他字段名
    extension_info: product?.extension_info,
  })

  // 处理自提商品，增加门店价格
  if (
    cloneProduct.extension_info?.store_info &&
    cloneProduct?.product?.price_range?.maximum_price?.final_price
  ) {
    // final_price = 商品 final_price + 门店价格
    cloneProduct.product.price_range.maximum_price.final_price.value = new Big(
      cloneProduct.product.price_range.maximum_price.final_price.value!,
    )
      .plus(new Big(cloneProduct.extension_info?.store_info?.price ?? 0))
      .toNumber()

    // regular_price = 商品 regular_price + 门店价格
    cloneProduct.product.price_range.maximum_price.regular_price.value = new Big(
      cloneProduct.product.price_range.maximum_price.regular_price.value!,
    )
      .plus(new Big(cloneProduct.extension_info?.store_info?.price ?? 0))
      .toNumber()
  }

  // 处理有安装服务产品，增加安装服务价格
  if (
    cloneProduct.extension_info?.custom_options &&
    cloneProduct?.product?.price_range?.maximum_price?.final_price
  ) {
    const customOptionsTotalPrice = cloneProduct.extension_info.custom_options.reduce(
      (totalPrice: Big.Big, nextOption) => {
        const optionTotalPrice = nextOption?.values?.reduce((valueTotalPrice: Big, nextValue) => {
          return valueTotalPrice.plus(new Big(nextValue?.price ?? 0))
        }, new Big(0))

        return totalPrice.plus(optionTotalPrice!)
      },
      new Big(0),
    )

    // final_price = 商品 final_price + 安装服务价格
    cloneProduct.product.price_range.maximum_price.final_price.value = new Big(
      cloneProduct.product.price_range.maximum_price.final_price.value!,
    )
      .plus(customOptionsTotalPrice)
      .toNumber()

    // regular_price = 商品 regular_price + 安装服务价格
    cloneProduct.product.price_range.maximum_price.regular_price.value = new Big(
      cloneProduct.product.price_range.maximum_price.regular_price.value!,
    )
      .plus(customOptionsTotalPrice)
      .toNumber()
  }

  return cloneProduct
}

/**
 * 格式化产品数据集合
 */
export const formatProducts = (products: TCheckoutCartProductItems) => {
  return products.map((product) => formatProduct(product!))
}

/**
 * 获取当前配置的属性对应的产品图片url
 */
interface ProductVariantAttribute {
  uid: string
}

interface ProductVariant {
  attributes: ProductVariantAttribute[]
  product: {
    image: {
      url: string
    }
  }
}

interface ProductOptionValue {
  uid: string
}

export const getProductImageUrl = (
  item: ProductOptionValue,
  variants: ProductVariant[],
): string => {
  const tempConfigUid: string[] = []
  let url = ''
  variants.forEach((variant) => {
    if (
      variant.attributes &&
      variant.attributes[0].uid === item.uid &&
      !tempConfigUid.includes(variant.attributes[0].uid)
    ) {
      tempConfigUid.push(item.uid)
      url = variant.product.image.url
    }
  })

  return url
}

/**
 * 格式化首页导航栏数据
 */
export const formatHomeNavs = (homeNavigation: HomeTopNavigation) =>
  homeNavigation?.map((item) => ({
    value: item?.page_identifier,
    label: item?.title,
    topCategories: item?.top_categories?.length
      ? item.top_categories.map((ite) => ({
          label: ite?.title,
          ...ite?.redirect,
        }))
      : [],
    categoryAllUid: item?.relation_category_uid,
    showProducts: item?.show_products,
  }))

/**
 * 格式化首页或专题页模块数据
 */
export const formatComponents = (homeComponents: HomePageComponents, currentTime: string) => {
  const newComponents: CurrentComponents[] = homeComponents.map((item) => ({
    ...item,
    showHorizonModule: item?.display_mode === 1,
    hasCountdown:
      !!currentTime &&
      Number(item?.activity_end_at) > Number(item?.activity_start_at) &&
      (Number(item?.activity_end_at) > Number(currentTime) ||
        Number(item?.activity_start_at) > Number(currentTime)),
    canShow:
      Number(item?.start_at) < Number(currentTime) && Number(item?.end_at) > Number(currentTime),
  }))

  return newComponents.filter((item) => item.canShow)
}

/**
 * 格式化首页导航栏数据
 */
export const formatHomeNavigation = (hadCar: boolean, homeTopNavigation: HomeTopNavigation) => {
  if (!homeTopNavigation?.length) {
    return []
  }

  // 首先根据sort字段进行排序
  const sortedNavigation = [...homeTopNavigation].sort((a, b) => {
    const sortA = a?.sort ?? 0
    const sortB = b?.sort ?? 0
    return sortA - sortB
  })

  return formatHomeNavs(sortedNavigation)
}

/**
 * 更新产品列表
 */
export const updateProductList = (prevProducts: HomeProduct[], productList: HomeProduct[]) => {
  const productIndexMap = new Map(prevProducts.map((product, index) => [product.sku, index]))

  const updatedProducts = [...prevProducts]

  productList.forEach((newProduct) => {
    if (productIndexMap.has(newProduct.sku)) {
      const existingIndex = productIndexMap.get(newProduct.sku)
      if (typeof existingIndex === 'number') {
        // 使用类型断言确保类型安全
        updatedProducts[existingIndex] = {
          ...updatedProducts[existingIndex],
          ...newProduct,
        } as HomeProduct
      }
    } else {
      updatedProducts.push(newProduct)
    }
  })

  return updatedProducts
}

/**
 * 生成游客id
 */
export const generateGustId = () => {
  const timestamp = Date.now().toString(36)
  const randomPart = Math.random().toString(36).substr(2, 9)
  return timestamp + randomPart
}
