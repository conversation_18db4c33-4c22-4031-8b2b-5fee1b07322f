import { gql } from '../../utils'

export const HOME_PRODUCT_FRAGMENT = gql`
  fragment homeProductFragment on ProductInterface {
    __typename
    name
    sku
    image {
      disabled
      label
      url
    }
    paymeng_method
    special_from_date_timestamp
    special_to_date_timestamp
    new_from_date
    new_to_date
    new_from_date_timestamp
    new_to_date_timestamp
    stock_status
    custom_attributesV3(
      filters: {
        filter_attributes: [
          "product_tag"
          "paymeng_method"
          "max_usage_limit_ncoins"
          "is_insurance"
          "insurance_link"
        ]
      }
    ) {
      items {
        code
        ... on AttributeValue {
          value
        }
        ... on AttributeSelectedOptions {
          selected_options {
            label
            value
          }
        }
      }
    }
  }
`
